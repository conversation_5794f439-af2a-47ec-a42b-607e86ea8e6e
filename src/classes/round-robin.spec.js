import { env } from 'cloudflare:test';
import { describe, expect, it, beforeEach } from 'vitest';
import { RoundRobin } from './round-robin.js';
import { Agents } from './agents.js';
import { MetaD1 } from './meta.js';
import { createTestAgents } from '../../test/testUtils.js';

describe('RoundRobin', () => {
  beforeEach(async () => {
    await MetaD1.delete(env, 'agents:list');
    await MetaD1.delete(env, 'round-robin:index');

    await Agents.setAgents(env, createTestAgents());
    await RoundRobin.setIndex(env, 0);
  });

  it('can get and set round-robin index', async () => {
    const rrAgents = await RoundRobin.getAgents(env);
    expect(rrAgents).toHaveLength(3);
    expect(rrAgents.map((a) => a.id)).toEqual(['agent-1', 'agent-3', 'agent-4']);

    // Test getIndex
    const initialIndex = await RoundRobin.getIndex(env);
    expect(initialIndex).toBe(0);

    // Test setIndex
    await RoundRobin.setIndex(env, 2);
    const updatedIndex = await RoundRobin.getIndex(env);
    expect(updatedIndex).toBe(2);

    // Test setIndex with float (should floor)
    await RoundRobin.setIndex(env, 1.7);
    const flooredIndex = await RoundRobin.getIndex(env);
    expect(flooredIndex).toBe(1);
  });

  it('can cycle through agents with next() and previous()', async () => {
    // Get the round-robin agents to know the exact order
    const rrAgents = await RoundRobin.getAgents(env);
    const rrAgentIds = rrAgents.map((a) => a.id);

    await assertNextBehavior(0, rrAgentIds[0], 1);
    await assertNextBehavior(1, rrAgentIds[1], 2);
    await assertNextBehavior(2, rrAgentIds[2], 0);
    // Should cycle back to first
    await assertNextBehavior(0, rrAgentIds[0], 1);

    await assertPreviousBehavior(1, rrAgentIds[1], 0);
    await assertPreviousBehavior(0, rrAgentIds[0], 2);
    await assertPreviousBehavior(2, rrAgentIds[2], 1);
    await assertPreviousBehavior(1, rrAgentIds[1], 0);
    // Should cycle back to last
    await assertPreviousBehavior(0, rrAgentIds[0], 2);

    async function assertNextBehavior(startIndex, expectedAgentId, finalIndex) {
      const index = await RoundRobin.getIndex(env);
      expect(index).toBe(startIndex);

      const agent = await RoundRobin.next(env);
      expect(agent.id).toBe(expectedAgentId);

      const currentIndex = await RoundRobin.getIndex(env);
      expect(currentIndex).toBe(finalIndex);
    }

    async function assertPreviousBehavior(startIndex, expectedAgentId, finalIndex) {
      const index = await RoundRobin.getIndex(env);
      expect(index).toBe(startIndex);

      const agent = await RoundRobin.previous(env);
      expect(agent.id).toBe(expectedAgentId);

      const currentIndex = await RoundRobin.getIndex(env);
      expect(currentIndex).toBe(finalIndex);
    }
  });

  it('can enable and disable agents', async () => {
    let rrAgents = await RoundRobin.getAgents(env);
    expect(rrAgents).toHaveLength(3);

    const enabledAgent = await RoundRobin.enableAgent(env, 'agent-2');
    expect(enabledAgent.roundRobin).toBe(true);

    rrAgents = await RoundRobin.getAgents(env);
    expect(rrAgents).toHaveLength(4);

    const disabledAgent = await RoundRobin.disableAgent(env, 'agent-1');
    expect(disabledAgent.roundRobin).toBe(false);

    rrAgents = await RoundRobin.getAgents(env);
    expect(rrAgents).toHaveLength(3);
    expect(rrAgents.map((a) => a.id)).not.toContain('agent-1');
  });

  it('handles edge cases and errors', async () => {
    await MetaD1.delete(env, 'agents:list');
    await MetaD1.delete(env, 'round-robin:index');

    await expect(RoundRobin.next(env)).rejects.toThrow('No agents available for round-robin assignment');
    await expect(RoundRobin.previous(env)).rejects.toThrow('No agents available for round-robin assignment');

    // Test with only non-round-robin agents
    const nonRRAgents = [{ id: 'agent-1', name: 'John', email: '<EMAIL>', roundRobin: false }];
    await Agents.setAgents(env, nonRRAgents);
    await expect(RoundRobin.next(env)).rejects.toThrow('No agents available for round-robin assignment');

    // Test setIndex with invalid values
    await expect(RoundRobin.setIndex(env, -1)).rejects.toThrow('Index must be a non-negative integer');
    await expect(RoundRobin.setIndex(env, 'invalid')).rejects.toThrow('Index must be a non-negative integer');

    // Test disabling the only remaining round-robin agent
    const singleRRAgent = [{ id: 'agent-1', name: 'John', email: '<EMAIL>', roundRobin: true }];
    await Agents.setAgents(env, singleRRAgent);
    await expect(RoundRobin.disableAgent(env, 'agent-1')).rejects.toThrow(
      'Cannot disable the only remaining agent. There must be at least one agent in round-robin.'
    );
  });

  it('handles index adjustments when enabling/disabling agents', async () => {
    // Set index to 1 (agent-3)
    await RoundRobin.setIndex(env, 1);

    // Enable agent-2 (will be inserted at index 1)
    await RoundRobin.enableAgent(env, 'agent-2');

    // Index should have shifted forward
    const newIndex = await RoundRobin.getIndex(env);
    expect(newIndex).toBe(2);

    // Disable agent at index 0 (agent-1)
    await RoundRobin.disableAgent(env, 'agent-1');

    // Index should have shifted back
    const adjustedIndex = await RoundRobin.getIndex(env);
    expect(adjustedIndex).toBe(1);
  });
});
