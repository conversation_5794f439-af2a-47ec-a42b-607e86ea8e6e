import { SELF, env } from 'cloudflare:test';
import { describe, expect, it, vi, beforeEach, beforeAll } from 'vitest';
import { ApplicationD1 } from '../../../db/applications.js';
import { SendToQueue } from '../../../queues/index.js';
import { Agents } from '../../../classes/agents.js';
import { MetaD1 } from '../../../classes/meta.js';
import { createTestAgents } from '../../../../test/testUtils.js';

const BASE_URL = 'http://localhost:8787';

const generateFastTrackPrequalData = (overrides = {}) => ({
  businessName: 'FastTrack Business LLC',
  firstName: 'Fast',
  lastName: 'Track',
  email: '<EMAIL>',
  phone: '1234567890',
  estimatedFICO: '700-780',
  consent: true,
  ...overrides,
});

const generateFastTrackRequest = (utm_rep = 'john.doe', overrides = {}) => ({
  preQualifyFields: generateFastTrackPrequalData(),
  domain: 'app.pinnaclefunding.com',
  utm: { utm_rep: utm_rep },
  ...overrides,
});

describe('Create App FastTrack Handler', () => {
  const testAgents = createTestAgents();

  beforeAll(async () => {
    await MetaD1.delete('agents:list');
    await Agents.setAgents(env, testAgents);
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('creates fasttrack application', async () => {
    const testAgent = testAgents[2];

    const requestData = generateFastTrackRequest(testAgent.email.split('@')[0]);

    const response = await SELF.fetch(`${BASE_URL}/app/fasttrack`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData),
    });

    expect(response.status).toBe(201);

    const data = await response.json();
    const agent = data.agent;
    expect(data).toMatchObject({
      uuid: expect.any(String),
      status: 'PREQUAL_FAST_TRACK',
    });
    expect(agent).toMatchObject({
      name: testAgent.name,
      email: testAgent.email,
    });

    const savedApp = await ApplicationD1.get(env, data.uuid);

    expect(savedApp).toMatchObject({
      uuid: data.uuid,
      status: 'PREQUAL_FAST_TRACK',
      preQualifyFields: requestData.preQualifyFields,
      domain: requestData.domain,
      fastTrack: true,
    });
  });

  it('requires valid utm_rep field', async () => {
    let requestData = generateFastTrackRequest();
    delete requestData.utm;

    let response = await SELF.fetch(`${BASE_URL}/app/fasttrack`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData),
    });

    expect(response.status).toBe(400);

    let data = await response.json();
    expect(data.error).toContain('Invalid utm_rep');

    requestData = generateFastTrackRequest({
      utm: { utm_rep: 'invalid-rep' },
    });

    response = await SELF.fetch(`${BASE_URL}/app/fasttrack`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData),
    });

    expect(response.status).toBe(400);

    data = await response.json();
    expect(data.error).toContain('Invalid utm_rep');
  });

  it('handles database errors gracefully', async () => {
    vi.spyOn(ApplicationD1, 'create').mockRejectedValueOnce(new Error('Database connection failed'));

    const requestData = generateFastTrackRequest();

    const response = await SELF.fetch(`${BASE_URL}/app/fasttrack`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData),
    });

    expect(response.status).toBe(500);

    const data = await response.json();
    expect(data.error).toBe('Internal Server Error');
    expect(data.errorId).toBeDefined();
  });

  it('handles queue errors gracefully but still creates application', async () => {
    vi.spyOn(SendToQueue, 'salseforce').mockRejectedValueOnce(new Error('Queue failed'));

    const requestData = generateFastTrackRequest();

    const response = await SELF.fetch(`${BASE_URL}/app/fasttrack`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestData),
    });

    expect(response.status).toBe(500);

    const data = await response.json();
    expect(data.error).toBe('Internal Server Error');
  });
});
